import 'package:bd/pages/baidu_index/controllers/baidu_index_log_controller%20.dart';
import 'package:bd/pages/baidu_index/controllers/baidu_index_task_controller%20.dart';
import 'package:bd/pages/baidu_index/controllers/baidu_index_user_controller.dart';
import 'package:flutter/material.dart';
import 'package:tekflat_design/tekflat_design.dart';
import 'package:bd/model/baidu_user_model.dart';
import 'package:bd/utils/toast_util.dart';
import '../baidu_index_logic.dart';
import 'package:get/get.dart';
/// 批量操作组件
/// 职责：全选复选框、批量操作按钮、添加账号、批量启用对话框
class BatchOperationsWidget extends StatelessWidget {
  final BaiduIndexLogic logic;
  final Function(BuildContext) onProxyConfigPressed;
  final Function(BuildContext) onBatchProxyPressed;
  LogController get logController => Get.find<LogController>();
  UserController get userController => Get.find<UserController>();
  TaskController get taskController => Get.find<TaskController>();
  const BatchOperationsWidget({
    Key? key,
    required this.logic,
    required this.onProxyConfigPressed,
    required this.onBatchProxyPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: TekSpacings().mainSpacing,
        vertical: 12,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左侧：全选复选框和标题
          Row(
            children: [
              Checkbox(
                value: userController.isAllSelected,
                onChanged: (value) => userController.toggleAllSelection(value ?? false),
              ),
              TekTypography(
                text: "百度指数账号管理（${userController.users.length}）",
              ),
            ],
          ),
          // 右侧：批量操作按钮组
          Row(
            children: [
              TekButton(
                size: TekButtonSize.small,
                type: TekButtonType.info,
                text: "代理配置",
                onPressed: userController.getSelectedUsers().isEmpty
                    ? null
                    : () => onProxyConfigPressed(context),
              ),
              SizedBox(width: 8),
              TekButton(
                size: TekButtonSize.small,
                type: TekButtonType.info,
                text: "批量设置代理",
                onPressed: userController.getSelectedUsers().isEmpty
                    ? null
                    : () => onBatchProxyPressed(context),
              ),
              SizedBox(width: 8),
              TekButton(
                size: TekButtonSize.small,
                type: TekButtonType.info,
                text: "批量启用",
                onPressed: userController.getSelectedUsers().isEmpty
                    ? null
                    : () => _showBatchEnableDialog(context),
              ),
              SizedBox(width: 8),
              TekButton(
                size: TekButtonSize.small,
                type: TekButtonType.info,
                text: "添加账号",
                onPressed: () => _handleAddAccount(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 显示批量启用对话框
  void _showBatchEnableDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('批量启用账号'),
          content: Text('确定要启用所有选中的账号吗？系统将检查每个账号是否满足启用条件。'),
          actions: <Widget>[
            TextButton(
              child: Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('确定'),
              onPressed: () => _handleBatchEnable(context),
            ),
          ],
        );
      },
    );
  }

  /// 处理批量启用
  void _handleBatchEnable(BuildContext context) {
    // 获取所有选中的账号
    List<BaiDuUsers> selectedUsers = userController.getSelectedUsers();
    List<String> errorMessages = [];

    // 检查每个账号并启用
    for (var user in selectedUsers) {
      if (user.isError) {
        errorMessages.add("${user.username}: 账号request block");
        continue;
      }
      if (user.cookie == null || user.cookie!.isEmpty) {
        errorMessages.add("${user.username}: 缺少Cookie");
        continue;
      }
      if (user.apiKey == null || user.apiKey!.isEmpty) {
        errorMessages.add("${user.username}: 缺少API Key");
        continue;
      }
      if (user.apiKeyTime == null || user.apiKeyTime!.isEmpty) {
        errorMessages.add("${user.username}: 缺少API Key时间");
        continue;
      }
      if (user.username == "暂未登录") {
        errorMessages.add("有账号未登录");
        continue;
      }

      // 通过验证，启用账号
      user.isStart = true;
    }

    // 更新UI
    logic.update(['list', 'two']);

    // 关闭对话框
    Navigator.of(context).pop();

    // 显示结果
    if (errorMessages.isEmpty) {
      showToast("所有选中账号已成功启用");
    } else {
      _showErrorDialog(context, errorMessages);
    }
  }

  /// 显示错误信息对话框
  void _showErrorDialog(BuildContext context, List<String> errorMessages) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('部分账号启用失败'),
          content: Container(
            width: 400,
            height: 200,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: errorMessages.map((msg) => Padding(
                  padding: EdgeInsets.only(bottom: 8),
                  child: Text(msg),
                )).toList(),
              ),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('确定'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  /// 处理添加账号
  void _handleAddAccount() {
    userController.users.add(BaiDuUsers(
      time_str: logic.formatDateTime(DateTime.now()),
      username: "暂未登录"
    ));
    logic.update(['list', "two"]);
  }
}
