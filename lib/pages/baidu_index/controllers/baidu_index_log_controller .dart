import 'dart:async';
import 'dart:convert';
import 'package:bd/pages/baidu_index/baidu_index_logic.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:puppeteer/protocol/network.dart';
import 'package:puppeteer/puppeteer.dart';
import '../../../model/baidu_user_model.dart';


class LogController extends GetxController {

  BaiduIndexLogic get baiduIndexLogic => Get.find<BaiduIndexLogic>();


  void addLog(String message) {
// 在开头插入新日志
    baiduIndexLogic.logs.insert(0, "[${DateTime.now().toString().split('.')[0]}] $message");

    // 如果超过500条，删除最老的日志（列表末尾的）
    if (baiduIndexLogic.logs.length > 500) {
      baiduIndexLogic.logs.removeRange(500, baiduIndexLogic.logs.length);  // 修改这里：删除500条之后的
    }

    baiduIndexLogic.update(['logs', 'logs_count']);

    // 优化滚动逻辑
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (baiduIndexLogic.logScrollController.hasClients) {
        baiduIndexLogic.logScrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void clearLogs() {
    baiduIndexLogic.logs.clear();
    baiduIndexLogic.update(['logs','logs_count']);
  }



}